@import 'tailwindcss';

@theme {
	--color-accent-200: #eebf87;
	--color-accent-300: #d59f6a;
	--color-accent-400: #bc804d;
	--color-accent-500: #a46233;
	--color-accent-600: #945e00;
	--color-accent-700: #7a4e00;
	--color-accent-800: #613d00;
	--color-accent-900: #482b00;
	--color-accent-950: #341e00;

	--color-gray-100: #f7f6f5;
	--color-gray-200: #efedeb;
	--color-gray-300: #c4c1bf;
	--color-gray-400: #908a85;
	--color-gray-500: #5c5752;
	--color-gray-600: #4a4541;
	--color-gray-700: #3c3733;
	--color-gray-800: #2a2622;
	--color-gray-900: #1a1816;
	--color-gray-950: #0e0d0b;
}

:root {
	--ease-in-quad: cubic-bezier(0.55, 0.085, 0.68, 0.53);
	--ease-in-cubic: cubic-bezier(0.55, 0.055, 0.675, 0.19);
	--ease-in-quart: cubic-bezier(0.895, 0.03, 0.685, 0.22);
	--ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
	--ease-in-expo: cubic-bezier(0.95, 0.05, 0.795, 0.035);
	--ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.335);

	--ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
	--ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
	--ease-out-quart: cubic-bezier(0.165, 0.84, 0.44, 1);
	--ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
	--ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
	--ease-out-circ: cubic-bezier(0.075, 0.82, 0.165, 1);

	--ease-in-out-quad: cubic-bezier(0.455, 0.03, 0.515, 0.955);
	--ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
	--ease-in-out-quart: cubic-bezier(0.77, 0, 0.175, 1);
	--ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);
	--ease-in-out-expo: cubic-bezier(1, 0, 0, 1);
	--ease-in-out-circ: cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

* {
	outline-color: var(--color-accent-500);
}

:focus-visible {
	outline: 4px solid var(--color-accent-500);
}

@utility abs-y-center {
	top: 50%;
	@apply -translate-y-1/2;
}
