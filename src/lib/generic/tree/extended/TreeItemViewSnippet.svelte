<script lang="ts" module>
	import type { Tree } from '../custom/customTreeFromTable';
	import type { TreeItemExt } from './treeFromTable';

	export { treeItemView as TreeItemViewDefault };
</script>

{#snippet treeItemView(item: TreeItemExt, treeState: Tree<TreeItemExt>)}
	<!-- svelte-ignore a11y_click_events_have_key_events -->
	<!-- svelte-ignore a11y_no_static_element_interactions -->
	<!-- svelte-ignore event_directive_deprecated -->
	<div
		class="ti relative"
		class:selected={item.selected}
		on:click={() => {
			if (treeState.selectedItem) {
				treeState.selectedItem.selected = false;
			}
			treeState.selectedItem = item;
			item.selected = true;
			item.expanded = !item.expanded;
		}}
	>
		<span>{item.title}</span>
	</div>
{/snippet}
