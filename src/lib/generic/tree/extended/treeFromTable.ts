import { buildCustomTreeFromTable, type TreeItem } from '../custom/customTreeFromTable';
import type { Row, Table } from '../tableTypes';

export type TreeItemExt = TreeItem & {
	children?: TreeItemExt[];
	title: string;
	leafCount: number;
};

export function buildTreeFromTable(table: Table, hierarchyColumns: string[]) {
	let serial = 0;
	const createItem = (row: Row, hierarchyColumnNum: number) => {
		return {
			id: ++serial,
			title: row[hierarchyColumns[hierarchyColumnNum]]?.toString() || 'Blank',
			leafCount: 0,
			children: undefined
		};
	};

	const sortFn = (a: TreeItemExt, b: TreeItemExt) => a.title.localeCompare(b.title);

	const calculateNodeValue = (t: TreeItemExt) => t.children?.length || 0;

	const accumulateToParent = (p: TreeItemExt, childValue: number) => {
		p.leafCount += childValue;
	};

	return buildCustomTreeFromTable<TreeItemExt, number>(
		table,
		hierarchyColumns,
		createItem,
		sortFn,
		calculateNodeValue,
		accumulateToParent
	);
}
