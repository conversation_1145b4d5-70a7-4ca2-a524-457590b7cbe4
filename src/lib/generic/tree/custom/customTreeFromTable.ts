import type { Row, Table } from '../tableTypes';

export type TreeItem = {
	id: number;
	children?: TreeItem[];
	expanded?: boolean;
	selected?: boolean;
	hidden?: boolean;
};

export type Tree<T extends TreeItem> = {
	children: T[];
	selectedItem: T | null;
};

type CreateItem = (
	row: Row,
	/**
	 * The column number in the hierarchy. Corresponds to the index of the `hierarchyColumn` array.
	 */
	hierarchyColumnNum: number
) => TreeItem;

export type Sort<T> = (a: T, b: T) => number;

/**
 * Tree will have maps temporarily during creation.
 */
type ItemTemp = TreeItem & {
	childrenMap?: Map<string, ItemTemp>;
};

/**
 *
 * @param table
 * @param hierarchyColumns
 * @param createItem
 * @param sort
 * @param calculateNodeValue executed after the construction of the full tree
 * @param accumulateToParent executed after the construction of the full tree
 * @returns
 */
export function buildCustomTreeFromTable<T extends TreeItem, U>(
	table: Table,
	hierarchyColumns: string[],
	createItem: CreateItem,
	sort: Sort<T>,
	calculateNodeValue: (t: T) => U,
	accumulateToParent: (p: T, childValue: U) => void
): Tree<T> {
	const tree = {};
	table.forEach((row) => {
		processRow(row, hierarchyColumns, createItem, tree as Tree<TreeItem>);
	});

	convertMapToArray(tree as T & ItemTemp, sort, calculateNodeValue, accumulateToParent);

	return tree as Tree<T>;
}

function processRow(
	row: Row,
	hierarchyColumns: string[],
	createItem: CreateItem,
	t: TreeItem | Tree<TreeItem>
) {
	let parent = t as ItemTemp;
	for (let i = 0; i < hierarchyColumns.length; i++) {
		const col = hierarchyColumns[i];
		const colValue = row[col]?.toString() || 'Blank';
		const r = parent.childrenMap?.get(colValue);
		if (r) {
			parent = r;
		} else {
			const newObj = createItem(row, i);
			if (!parent.childrenMap) {
				parent.childrenMap = new Map<string, ItemTemp>();
			}
			parent.childrenMap?.set(colValue, newObj);
			parent = newObj;
		}
	}
}

/**
 * - Converts the childrenMap to children array
 * - Accumulates the child values to the parent
 * - Sorts the children array
 * - Calls itself recursively for each child
 */
function convertMapToArray<T extends TreeItem, U>(
	t: T & ItemTemp,
	sortFn: Sort<T>,
	calculateNodeValue: (t: T) => U,
	accumulateToParent: (p: T, childValue: U) => void
) {
	if (t.childrenMap?.size) {
		t.children = Array.from(t.childrenMap?.values() || []);
		delete t.childrenMap;
		(t.children as T[]).sort(sortFn);
		t.children.forEach((a) => {
			const childValue = convertMapToArray(a as T, sortFn, calculateNodeValue, accumulateToParent);
			accumulateToParent(t, childValue);
		});
	}
	return calculateNodeValue(t);
}
