<script lang="ts" generics="T extends TreeItem">
	import type { Tree, TreeItem } from '$lib/generic/tree/custom/customTreeFromTable';
	import type { Snippet } from 'svelte';

	const { treeData, treeItemView }: { treeData: Tree<T>; treeItemView: Snippet<[T, Tree<T>]> } =
		$props();
</script>

{#snippet treeItems(children: Iterable<T>, tree: Tree<T>, depth = 0)}
	<ul class="relative">
		{#if depth > 0}
			<div
				class="absolute top-2 bottom-2 w-px bg-gray-200 dark:bg-gray-700"
				style="left: {0.5 + depth * 1}rem"
			></div>
		{/if}
		{#each children as item (item.id)}
			{#if !item.hidden}
				<li class="cursor-pointer">
					<div style="padding-left: {(depth + 1) * 1}rem">
						{@render treeItemView(item, tree)}
					</div>
					{#if item.children && item.expanded}
						{@render treeItems(item.children as T[], tree, depth + 1)}
					{/if}
				</li>
			{/if}
		{/each}
	</ul>
{/snippet}

<div
	class="relative grid min-h-[500px] overflow-auto rounded-2xl border
	bg-gray-100 text-gray-700 dark:border-gray-700 dark:bg-gray-950 dark:text-gray-400"
>
	<div class="w-full min-w-0 overflow-clip p-2">
		<ul class="list-none rounded-md p-2">
			{@render treeItems(treeData.children || [], treeData)}
		</ul>
	</div>
</div>
