import type { TreeItem } from './custom/customTreeFromTable';

export function treeForEach<T extends TreeItem>(children: T[], fn: (item: T) => void) {
	children.forEach((c) => {
		fn(c);
		if (c.children) {
			treeForEach(c.children as T[], fn);
		}
	});
}

type TreeOrItem<T extends TreeItem> = { children?: T[] };

export function treeAccumulate<T extends TreeItem, U>(
	t: TreeOrItem<T>,
	initialize: (t: T) => void,
	calculateNodeValue: (t: T) => U,
	accumulateToParent: (p: T, childValue: U) => void
) {
	initialize(t as T);
	if (t.children?.length) {
		t.children.forEach((a) => {
			const childValue = treeAccumulate(
				a as TreeOrItem<T>,
				initialize,
				calculateNodeValue,
				accumulateToParent
			);
			accumulateToParent(t as T, childValue);
		});
	}
	return calculateNodeValue(t as T);
}
