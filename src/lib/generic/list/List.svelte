<script lang="ts" module>
	type ListItemInternal = {
		id: string;
		title: string;
		selected?: boolean;
	};

	type WithReadonly<T, K extends keyof T> = Readonly<Pick<T, K>> & Omit<T, K>;

	export type ListItem = WithReadonly<ListItemInternal, 'selected'>;
</script>

<script lang="ts">
	let {
		items,
		onSelection: onSelection
	}: { items: ListItem[]; onSelection?: (item: ListItem) => void } = $props();

	let listState = {
		_selected: undefined as ListItemInternal | undefined,
		get selected() {
			return this._selected;
		},
		set selected(value: ListItemInternal | undefined) {
			if (this._selected) {
				this._selected.selected = false;
			}
			this._selected = value;
			if (value) {
				value.selected = true;
			}
		}
	};

	export function clearSelection() {
		listState.selected = undefined;
	}
</script>

<div>
	<ul class="list-none rounded-md p-2">
		{#each items as item (item.id)}
			<li class="cursor-pointer">
				<!-- svelte-ignore a11y_click_events_have_key_events -->
				<!-- svelte-ignore a11y_no_static_element_interactions -->
				<!-- svelte-ignore event_directive_deprecated -->
				<div
					class:selected={item.selected}
					class="pl-2"
					on:click={() => {
						listState.selected = item;
						if (onSelection) {
							onSelection(item);
						}
					}}
				>
					<span>{item.title}</span>
				</div>
			</li>
		{/each}
	</ul>
</div>

<style>
	.selected {
		background-color: #a46233;
		color: white;
		border-radius: 0.75rem;
	}
</style>
