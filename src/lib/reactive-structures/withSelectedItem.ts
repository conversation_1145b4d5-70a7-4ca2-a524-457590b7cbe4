import type { Writable } from 'type-fest';
import type { TreeItem } from './tree.svelte';

/**
 * Mixin to add selected item logic to a tree-like structure.
 */
export function withSelectedItem<T extends TreeItem, Base extends new (...args: any[]) => object>(
	BaseClass: Base
) {
	return class extends BaseClass {
		#selectedItem: Writable<TreeItem> | null = null;

		get selectedItem(): T | null {
			return this.#selectedItem as T | null;
		}

		set selectedItem(value: T | null) {
			if (this.#selectedItem) {
				this.#selectedItem.selected = false;
			}
			this.#selectedItem = value;
			if (this.#selectedItem) {
				this.#selectedItem.selected = true;
			}
		}
	};
}
