import type { Writable } from 'type-fest';
import { SvelteSet } from 'svelte/reactivity';

export type TreeItem = {
	id: number;
	children?: TreeItem[];
	expanded?: boolean;
	readonly selected?: boolean;
	hidden?: boolean;
};

export function createSelectableMixin<T extends TreeItem>() {
	return class SelectableMixin {
		#selectedItem: Writable<T> | null = $state(null);

		get selectedItem() {
			return this.#selectedItem as T | null;
		}

		set selectedItem(value: T | null) {
			if (this.#selectedItem) {
				this.#selectedItem.selected = false;
			}
			this.#selectedItem = value as Writable<T> | null;
			if (this.#selectedItem) {
				this.#selectedItem.selected = true;
			}
		}
	};
}
export function createMultiSelectableMixin<T extends TreeItem>() {
	return class MultiSelectableMixin {
		#selectedItems: SvelteSet<Writable<T>> = $state(new SvelteSet());

		get selectedItems() {
		get selectedItems() {
			return Array.from(this.#selectedItems) as T[];
		}

		selectItem(item: T) {
			const writableItem = item as Writable<T>;
			this.#selectedItems.add(writableItem);
			writableItem.selected = true;
		}

		deselectItem(item: T) {
			const writableItem = item as Writable<T>;
			this.#selectedItems.delete(writableItem);
			writableItem.selected = false;
		}

		toggleItemSelection(item: T) {
			if (this.isItemSelected(item)) {
				this.deselectItem(item);
			} else {
				this.selectItem(item);
			}
		}

		isItemSelected(item: T) {
			return this.#selectedItems.has(item as Writable<T>);
		}

		clearSelection() {
			for (const item of this.#selectedItems) {
				item.selected = false;
			}
			this.#selectedItems.clear();
		}
	};
}

export class Tree<T extends TreeItem> extends createSelectableMixin() {
	#children: Writable<TreeItem>[];

	constructor(children: T[]) {
		super();
		this.#children = $state(children);
	}

	get children() {
		return this.#children as T[];
	}
}
