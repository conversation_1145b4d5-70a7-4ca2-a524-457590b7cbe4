import { pgTable, pgSchema, varchar, integer } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const neldata = pgSchema("neldata");


export const locationHierarchyInNeldata = neldata.table("location_hierarchy", {
	assetItem: varchar("Asset Item", { length: 50 }),
	assetIdValidationCategory: varchar("Asset ID Validation Category", { length: 50 }),
	description: varchar("Description", { length: 5000 }),
	owner: varchar("Owner", { length: 50 }),
	subType: varchar("SubType", { length: 50 }),
	location: varchar("Location", { length: 5000 }),
	wbs: varchar("WBS", { length: 5000 }),
	wbsDescription: varchar("WBS Description", { length: 5000 }),
	controlLine: varchar("ControlLine", { length: 50 }),
	chainage: varchar("Chainage", { length: 50 }),
	column11: integer("Column11"),
	locationId: varchar("Location ID", { length: 50 }),
	locationDescription: varchar("Location Description", { length: 5000 }),
	chainage1: varchar("Chainage_1", { length: 50 }),
	controlLine1: varchar("ControlLine_1", { length: 50 }),
	plant: varchar("Plant", { length: 50 }),
	facility: varchar("Facility", { length: 50 }),
	area: varchar("Area", { length: 5000 }),
	location1: varchar("Location_1", { length: 5000 }),
	column20: varchar("Column20", { length: 50 }),
	column21: varchar("Column21", { length: 50 }),
});

export const aicmInNeldata = neldata.table("aicm", {
	dpk: integer("DPK"),
	highLevelDiscipline: varchar("High_Level_Discipline", { length: 5000 }),
	discipline: varchar("Discipline", { length: 5000 }),
	component: varchar("Component", { length: 5000 }),
	elementType: varchar("Element_Type", { length: 5000 }),
	cjvCode: varchar("CJV_Code", { length: 5000 }),
	modelled: varchar("Modelled", { length: 5000 }),
	ventiaDiscipline: varchar("Ventia_Discipline", { length: 5000 }),
	system: varchar("System", { length: 5000 }),
	assetType: varchar("Asset_Type", { length: 5000 }),
	subAssetType: varchar("Sub_Asset_Type", { length: 5000 }),
	ventiaParentCode: varchar("Ventia_Parent_Code", { length: 5000 }),
	ventiaChildCode: varchar("Ventia_Child_Code", { length: 5000 }),
	activelyMaintained: varchar("actively_maintained", { length: 5000 }),
});
