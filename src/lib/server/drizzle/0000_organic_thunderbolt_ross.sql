-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE SCHEMA "neldata";
--> statement-breakpoint
CREATE TABLE "neldata"."location_hierarchy" (
	"Asset Item" varchar(50),
	"Asset ID Validation Category" varchar(50),
	"Description" varchar(5000),
	"Owner" varchar(50),
	"SubType" varchar(50),
	"Location" varchar(5000),
	"WBS" varchar(5000),
	"WBS Description" varchar(5000),
	"ControlLine" varchar(50),
	"Chainage" varchar(50),
	"Column11" integer,
	"Location ID" varchar(50),
	"Location Description" varchar(5000),
	"Chainage_1" varchar(50),
	"ControlLine_1" varchar(50),
	"Plant" varchar(50),
	"Facility" varchar(50),
	"Area" varchar(5000),
	"Location_1" varchar(5000),
	"Column20" varchar(50),
	"Column21" varchar(50),
	"Column22" varchar(50),
	"Column23" varchar(50),
	"Column24" varchar(50),
	"Column25" varchar(50),
	"Column26" varchar(50),
	"Column27" varchar(50),
	"Column28" varchar(50),
	"Column29" varchar(50),
	"Column30" varchar(50),
	"Column31" varchar(50),
	"Column32" varchar(50),
	"Column33" varchar(50),
	"Column34" varchar(50),
	"Column35" varchar(50),
	"Column36" varchar(50),
	"Column37" varchar(50),
	"Column38" varchar(50),
	"Column39" varchar(50),
	"Column40" varchar(50),
	"Column41" varchar(50),
	"Column42" varchar(50),
	"Column43" varchar(50),
	"Column44" varchar(50),
	"Column45" varchar(50),
	"Column46" varchar(50),
	"Column47" varchar(50),
	"Column48" varchar(50),
	"Column49" varchar(50),
	"Column50" varchar(50),
	"Column51" varchar(50),
	"Column52" varchar(50),
	"Column53" varchar(50),
	"Column54" varchar(50),
	"Column55" varchar(50),
	"Column56" varchar(50),
	"Column57" varchar(50),
	"Column58" varchar(50),
	"Column59" varchar(50),
	"Column60" varchar(50),
	"Column61" varchar(50),
	"Column62" varchar(50),
	"Column63" varchar(50),
	"Column64" varchar(50),
	"Column65" varchar(50),
	"Column66" varchar(50),
	"Column67" varchar(50),
	"Column68" varchar(50),
	"Column69" varchar(50),
	"Column70" varchar(50),
	"Column71" varchar(50),
	"Column72" varchar(50),
	"Column73" varchar(50),
	"Column74" varchar(50),
	"Column75" varchar(50),
	"Column76" varchar(50),
	"Column77" varchar(50),
	"Column78" varchar(50),
	"Column79" varchar(50),
	"Column80" varchar(50),
	"Column81" varchar(50),
	"Column82" varchar(50),
	"Column83" varchar(50),
	"Column84" varchar(50),
	"Column85" varchar(50),
	"Column86" varchar(50),
	"Column87" varchar(50),
	"Column88" varchar(50),
	"Column89" varchar(50),
	"Column90" varchar(50),
	"Column91" varchar(50),
	"Column92" varchar(50),
	"Column93" varchar(50),
	"Column94" varchar(50),
	"Column95" varchar(50),
	"Column96" varchar(50),
	"Column97" varchar(50),
	"Column98" varchar(50),
	"Column99" varchar(50),
	"Column100" varchar(50),
	"Column101" varchar(50),
	"Column102" varchar(50),
	"Column103" varchar(50),
	"Column104" varchar(50),
	"Column105" varchar(50),
	"Column106" varchar(50),
	"Column107" varchar(50),
	"Column108" varchar(50),
	"Column109" varchar(50),
	"Column110" varchar(50),
	"Column111" varchar(50),
	"Column112" varchar(50),
	"Column113" varchar(50),
	"Column114" varchar(50),
	"Column115" varchar(50),
	"Column116" varchar(50),
	"Column117" varchar(50),
	"Column118" varchar(50),
	"Column119" varchar(50),
	"Column120" varchar(50),
	"Column121" varchar(50),
	"Column122" varchar(50),
	"Column123" varchar(50),
	"Column124" varchar(50),
	"Column125" varchar(50),
	"Column126" varchar(50),
	"Column127" varchar(50)
);

*/