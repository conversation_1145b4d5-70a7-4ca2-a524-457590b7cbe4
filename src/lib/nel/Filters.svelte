<script lang="ts">
	import AssetTypeList from './AssetTypeList.svelte';
	import ControlLineList from './ControlLineList.svelte';
	import type { FilterCriteria } from './filters/filterData';
	import FiltersTabHeader from './FiltersTabHeader.svelte';

	let {
		data,
		onSelection: onSelection
	}: {
		data: { assetItem: string | null; controlLine: string | null; chainage: string | null }[];
		onSelection?: (filterCriteria: FilterCriteria) => void;
	} = $props();

	let filterCriteria: FilterCriteria = $state({});

	function onAssetTypeSelection(item: { id: string; title: string }) {
		filterCriteria.assetType = item.title === '' ? undefined : item.title;
		if (onSelection) {
			onSelection(filterCriteria);
		}
	}

	let filterTab: 'assetType' | 'chainage' = $state('assetType');
	let controlLineList: ControlLineList;

	function onTextBoxValueChange() {
		if (onSelection) {
			onSelection(filterCriteria);
		}
	}
</script>

<FiltersTabHeader bind:filterTab />

<div style:display={filterTab === 'assetType' ? 'block' : 'none'}>
	<AssetTypeList {data} onSelection={onAssetTypeSelection} />
</div>
<div style:display={filterTab === 'chainage' ? 'block' : 'none'}>
	<div class="flex place-items-center gap-4 rounded-2xl border-1 p-2">
		<img src="/images/icons/asset.svg" alt="asset" class="h-8 w-8" />
		<h2 class="mr-2 text-xl font-bold">Chainage Filter</h2>
		<button
			type="button"
			class="rounded-full bg-blue-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
			onclick={() => {
				filterCriteria.controlLine = undefined;
				filterCriteria.startChainage = undefined;
				filterCriteria.endChainage = undefined;
				filterCriteria.startChainage = undefined;
				filterCriteria.endChainage = undefined;
				controlLineList.clearSelection();
				if (onSelection) {
					onSelection(filterCriteria);
				}
			}}>Clear filter</button
		>
	</div>
	<div class="">
		<label class="p-2" for="start_chainage">Start Chainage:</label><input
			class="w-20 p-2"
			id="start_chainage"
			type="number"
			bind:value={filterCriteria.startChainage}
			onchange={onTextBoxValueChange}
		/>
		<label class="p-2" for="end_chainage">End Chainage:</label><input
			class="w-20 p-2"
			id="end_chainage"
			type="number"
			bind:value={filterCriteria.endChainage}
			onchange={onTextBoxValueChange}
		/>
	</div>
	<ControlLineList
		{data}
		onSelection={(item) => {
			filterCriteria.controlLine = item.id === '' ? undefined : item.id;
			if (onSelection) {
				onSelection(filterCriteria);
			}
		}}
		bind:this={controlLineList}
	/>
</div>
