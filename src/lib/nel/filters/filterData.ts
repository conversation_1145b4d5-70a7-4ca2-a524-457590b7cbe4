import { treeAccumulate } from '$lib/generic/tree/treeHelpers';
import type { Tree } from '$lib/generic/tree/custom/customTreeFromTable';
import type { TreeItemAdv } from '$lib/generic/tree/advanced/treeTypes';

export type FilterCriteria = {
	assetType?: string;
	controlLine?: string;
	startChainage?: number;
	endChainage?: number;
};

function isFilterCriteriaBlank(f: FilterCriteria) {
	return !f.assetType && !f.controlLine && !f.startChainage && !f.endChainage;
}

export function filterData(tree: Tree<TreeItemAdv>, filterCriteria: FilterCriteria) {
	if (isFilterCriteriaBlank(filterCriteria)) {
		treeAccumulate(
			tree,
			(c) => {
				c.visibleCount = 0;
			},
			(c) => {
				c.hidden = false;
				return 0;
			},
			() => {}
		);
	} else {
		treeAccumulate(
			tree,
			(c) => {
				c.visibleCount = 0;
			},
			(c) => {
				if (c.icon === 'asset') {
					c.hidden = filterCriteria.assetType
						? !c.title.startsWith(filterCriteria.assetType)
						: false;
					c.hidden =
						c.hidden ||
						(filterCriteria.controlLine ? c.controlLine !== filterCriteria.controlLine : false);
					c.hidden =
						c.hidden ||
						(filterCriteria.startChainage !== undefined && c.chainage !== undefined
							? c.chainage < filterCriteria.startChainage
							: false);
					c.hidden =
						c.hidden ||
						(filterCriteria.endChainage !== undefined && c.chainage !== undefined
							? c.chainage > filterCriteria.endChainage
							: false);
					return c.hidden ? 0 : 1;
				} else {
					return c.visibleCount;
				}
			},
			(c, childValue) => {
				c.visibleCount += childValue;
			}
		);
	}
}
