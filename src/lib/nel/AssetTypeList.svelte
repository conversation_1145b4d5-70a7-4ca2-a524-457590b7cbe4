<script lang="ts">
	import List, { type ListItem } from '../generic/list/List.svelte';

	let {
		data,
		onSelection = $bindable()
	}: {
		data: { assetItem: string | null }[];
		onSelection?: (item: { id: string; title: string }) => void;
	} = $props();

	// svelte-ignore non_reactive_update
	let list: List;

	let id = 0;

	const set = new Set(data.map((a) => (a.assetItem || 'BLK').substring(0, 3)));

	function createItems() {
		return Array.from(set)
			.sort()
			.map((a) => ({ id: (++id).toString(), title: a }));
	}

	const items = $state(createItems());
</script>

<div class="flex place-items-center gap-4 rounded-2xl border-1 p-2">
	<img src="/images/icons/asset.svg" alt="asset" class="h-8 w-8" />
	<h2 class="mr-2 text-xl font-bold">Asset Type filter</h2>
	<button
		type="button"
		class="rounded-full bg-blue-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
		onclick={() => {
			if (onSelection) {
				list.clearSelection();
				onSelection({ id: '', title: '' });
			}
		}}>Clear filter</button
	>
</div>

<div class="mt-0.5 rounded-2xl border-1">
	<List {items} {onSelection} bind:this={list} />
</div>
