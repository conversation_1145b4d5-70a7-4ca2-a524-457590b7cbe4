<script lang="ts">
	import List from '../generic/list/List.svelte';

	let {
		data,
		onSelection = $bindable()
	}: {
		data: { controlLine: string | null; chainage: string | null }[];
		onSelection?: (item: { id: string; title: string }) => void;
	} = $props();

	//let id = 0;

	const map = new Map<string, { min: number; max: number }>();
	data.forEach((a) => {
		const key = a.controlLine || 'BLK';
		const value = map.get(key);
		if (value) {
			value.min = Math.min(value.min, Number(a.chainage) || 0);
			value.max = Math.max(value.max, Number(a.chainage) || 0);
		} else {
			map.set(key, { min: Number(a.chainage) || 0, max: Number(a.chainage) || 0 });
		}
	});

	//const set = new Set(data.map((a) => a.controlLine || 'BLK'));
	function createItems() {
		return Array.from(map.entries())
			.sort((a, b) => a[0].localeCompare(b[0]))
			.map((a) => ({
				id: a[0],
				title: a[0] + ' (' + a[1].min + ', ' + a[1].max + ')'
			}));
	}
	const items = $state(createItems());

	let list: List;
	export function clearSelection() {
		list.clearSelection();
	}
</script>

<div class="mt-0.5 rounded-2xl border-1">
	<List {items} {onSelection} bind:this={list} />
</div>
