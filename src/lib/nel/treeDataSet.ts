import type { TreeItemAdv } from '$lib/generic/tree/advanced/treeTypes';
import type { Row, Table } from '$lib/generic/tree/tableTypes';
import { buildCustomTreeFromTable } from '../generic/tree/custom/customTreeFromTable';

export function buildTreeFromTable(table: Table) {
	let serial = 0;

	const hierarchyColumns = ['plant', 'facility', 'area', 'location1', 'assetItem'];

	const createItem = (row: Row, hierarchyColNum: number) => {
		const col = hierarchyColumns[hierarchyColNum];

		let title = '';
		let icon = '';
		let controlLine = undefined;
		let chainage = undefined;

		switch (hierarchyColNum) {
			case 0:
				icon = 'plant';
				title = row[col]?.toString() || 'Blank';
				break;
			case 1:
				icon = 'facility';
				title = row[col]?.toString() || 'Blank';
				break;
			case 2:
				icon = 'area';
				title = row[col]?.toString() || 'Blank';
				break;
			case 3:
				icon = 'location';
				title = row[col]?.toString() || 'Blank';
				break;
			case 4:
				icon = 'asset';
				title = row[col] ? row[col].toString() + ' - ' + row['description'] : 'Blank';
				controlLine = row['controlLine']?.toString();
				chainage = row['chainage'] ? Number(row['chainage']) : undefined;
				break;
		}

		return {
			id: ++serial,
			title: title,
			childrenCount: 0,
			leafCount: 0,
			visibleCount: 0,
			children: undefined,
			icon,
			controlLine,
			chainage
		};
	};

	const sortFn = (a: TreeItemAdv, b: TreeItemAdv) => a.title.localeCompare(b.title);

	const calculateNodeValue = (t: TreeItemAdv) => {
		t.childrenCount = t.children?.length || 0;
		return t.icon === 'asset' ? 1 : t.leafCount;
	};

	const accumulateToParent = (p: TreeItemAdv, childValue: number) => {
		p.leafCount += childValue;
	};

	return buildCustomTreeFromTable<TreeItemAdv, number>(
		table,
		hierarchyColumns,
		createItem,
		sortFn,
		calculateNodeValue,
		accumulateToParent
	);
}
