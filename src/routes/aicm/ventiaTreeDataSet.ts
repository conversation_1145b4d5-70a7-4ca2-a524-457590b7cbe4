import type { TreeItemAdv } from '$lib/generic/tree/advanced/treeTypes';
import type { Row, Table } from '$lib/generic/tree/tableTypes';
import { buildCustomTreeFromTable } from '$lib/generic/tree/custom/customTreeFromTable';

export function buildTreeFromTable(table: Table) {
	let serial = 0;

	const hierarchyColumns = ['ventiaDiscipline', 'system', 'assetType', 'subAssetType'];

	const createItem = (row: Row, hierarchyColNum: number) => {
		const col = hierarchyColumns[hierarchyColNum];

		let title = '';
		let icon = '';

		switch (hierarchyColNum) {
			case 0:
				icon = 'discipline';
				title = row[col]?.toString() || 'Blank';
				break;
			case 1:
				icon = 'system';
				title = row[col]?.toString() || 'Blank';
				break;
			case 2:
				icon = 'asset1';
				title = row[col]?.toString() || 'Blank';
				break;
			case 3:
				icon = 'subasset';
				title = row[col]?.toString() || 'Blank';
				break;
		}

		return {
			id: ++serial,
			title: title,
			childrenCount: 0,
			leafCount: 0,
			visibleCount: 0,
			children: undefined,
			icon
		};
	};

	const sortFn = (a: TreeItemAdv, b: TreeItemAdv) => a.title.localeCompare(b.title);

	const calculateNodeValue = (t: TreeItemAdv) => {
		t.childrenCount = t.children?.length || 0;
		return t.icon === 'subasset' ? 1 : t.leafCount;
	};

	const accumulateToParent = (p: TreeItemAdv, childValue: number) => {
		p.leafCount += childValue;
	};

	return buildCustomTreeFromTable<TreeItemAdv, number>(
		table,
		hierarchyColumns,
		createItem,
		sortFn,
		calculateNodeValue,
		accumulateToParent
	);
}
