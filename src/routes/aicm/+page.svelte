<script lang="ts">
	import CompactButton from '$lib/generic/button/CompactButton.svelte';
	import { TreeItemViewAdvanced } from '$lib/generic/tree/advanced/TreeItemViewSnippet.svelte';
	import MyTreeView from '../../lib/generic/tree/custom/MyTreeView.svelte';
	import { getData } from './data.remote';
	import { buildTreeFromTable } from './ventiaTreeDataSet';
	import { buildTreeFromTable as buildDncTreeFromTable } from './dncTreeDataSet';

	const dataSet = await getData();

	const tree = buildTreeFromTable(dataSet);
	let treeState = $state(tree);

	const dncTree = buildDncTreeFromTable(dataSet);
	let dncTreeState = $state(dncTree);

	let compact = $state(false);
</script>

<p class="text-center text-xl">Asset Classification</p>

<CompactButton bind:compact />

<div class="flex flex-1 gap-4 overflow-hidden">
	<div class="w-1/2 flex-1 overflow-y-auto">
		<div
			class="m-auto w-20 rounded-t-2xl border-t-1 border-r-1 border-l-1 bg-emerald-200 text-center"
		>
			Ventia
		</div>
		<MyTreeView
			treeData={treeState}
			treeItemView={TreeItemViewAdvanced}
			--var-padding={compact ? 0 : '0.5rem'}
		/>
	</div>
	<div class="w-1/2 flex-1 overflow-y-auto">
		<div class="m-auto w-20 rounded-t-2xl border-t-1 border-r-1 border-l-1 bg-blue-200 text-center">
			D&C
		</div>
		<MyTreeView
			treeData={dncTreeState}
			treeItemView={TreeItemViewAdvanced}
			--var-padding={compact ? 0 : '0.5rem'}
		/>
	</div>
</div>
