import { prerender } from '$app/server';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Client } from 'pg';
import { aicmInNeldata } from '$lib/server/drizzle/schema';

//console.log('database url', process.env.DATABASE_URL); //not working -- can't access env variables
const client = new Client({
	connectionString: 'postgres://localhost:5432/creditmap_db'
});

await client.connect();

const db = drizzle(client);

export const getData = prerender(async () => {
	const data = await db.select().from(aicmInNeldata);
	return data;
});
