<script lang="ts">
	import '../app.css';
	import favicon from '$lib/assets/favicon.svg';

	let { children } = $props();
</script>

<svelte:head>
	<link rel="icon" href={favicon} />
</svelte:head>

<svelte:boundary>
	<div class="m-2 dark:bg-gray-800 dark:text-gray-400">
		<div class="flex h-screen flex-col">
			<div class="flex place-content-center place-items-center gap-2">
				<img src="src/lib/assets/favicon.svg" alt="tunnel" class="h-12 w-12" />
				<h1 class="text-3xl">Welcome to AIMS (Asset Information Management System) - NEL</h1>
			</div>
			{@render children?.()}
		</div>
	</div>
	{#snippet pending()}
		<div>Loading...</div>
	{/snippet}
</svelte:boundary>

<style>
	:global(body) {
		overflow: hidden;
	}
</style>
