<script module lang="ts">
	import { Tree, type TreeItem } from 'melt/builders';
	import Motion from './Motion.svelte';

	export type Item = TreeItem & {
		title: string;
		icon: string;
		children?: Item[];
	};
</script>

<script lang="ts">
	const { items }: { items: Item[] } = $props();

	const tree = new Tree({
		items: items,
		// expanded: ['lib', 'routes'],
		expandOnClick: true
	});
</script>

{#snippet treeItemIcon(item: (typeof tree)['children'][number])}
	{@const icon = item.item.icon}

	<img src="/images/icons/{icon}.svg" alt={icon} class="h-4 w-4" />
{/snippet}

{#snippet treeItems(items: (typeof tree)['children'], depth: number = 0)}
	{#each items as item (item.id)}
		<li
			{...item.attrs}
			class="cursor-pointer rounded-sm !outline-none first:mt-0 [&:focus-visible>:first-child>div]:ring-4"
		>
			<div class="group py-1" style="padding-left: {depth * 1}rem">
				<div
					class="{item.selected
						? '!bg-accent-500 !text-white dark:!bg-accent-200 dark:!text-accent-950'
						: ''}
					flex h-full w-full items-center gap-2 rounded-xl px-3 py-1
					ring-accent-500 ring-offset-white transition group-hover:bg-gray-200 dark:ring-accent-700
					dark:ring-offset-black dark:group-hover:bg-gray-800"
				>
					{@render treeItemIcon(item)}
					<span class="select-none">
						{item.item.title}
					</span>
					<span
						class="me-2 ml-auto rounded-sm bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-300"
						>{item.children?.length}</span
					>
					<span>/</span>
					<span
						class="me-2 rounded-sm bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300"
						>{item.children?.length}</span
					>
				</div>
			</div>
			{#if item.children?.length}
				<Motion
					tag="ul"
					animate={{
						height: item.expanded ? 'auto' : 0,
						opacity: item.expanded ? 1 : 0,
						scale: item.expanded ? 1 : 0.85
					}}
					transition={{
						height: { delay: item.expanded ? 0 : 0.1 },
						opacity: { ease: 'easeOut', delay: item.expanded ? 0.1 : 0, duration: 0.2 },
						type: 'spring',
						stiffness: 200,
						damping: 20,
						mass: 0.15,
						bounce: 1
					}}
					{...tree.group}
					class="relative list-none p-0 {!item.expanded ? 'pointer-events-none' : ''} origin-left"
				>
					<div
						class="absolute top-2 bottom-2 w-px bg-gray-200 dark:bg-gray-700"
						style="left: {0.5 + depth * 1}rem"
					></div>
					{@render treeItems(item.children, depth + 1)}
				</Motion>
			{/if}
		</li>
	{/each}
{/snippet}

<div
	class="not-content relative grid min-h-[500px] place-items-center overflow-clip rounded-2xl border
	bg-gray-100 text-gray-700 dark:border-gray-700 dark:bg-gray-950 dark:text-gray-400"
>
	<div class="w-full min-w-0 overflow-clip p-4">
		<ul class="mx-auto w-[600px] list-none rounded-md p-4" {...tree.root}>
			{@render treeItems(tree.children, 0)}
		</ul>
	</div>
</div>
